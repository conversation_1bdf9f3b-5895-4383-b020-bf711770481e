package project

import (
	"encoding/json"
	"errors"
	"log/slog"
	"net/http"
	"strconv"

	"github.com/koopa0/pms-api-v2/internal/api"
	"github.com/koopa0/pms-api-v2/internal/business/auth"
	"github.com/koopa0/pms-api-v2/internal/constants"
	"github.com/koopa0/pms-api-v2/internal/validator"
	"github.com/koopa0/pms-api-v2/sqlc"
)

// Handler handles HTTP requests for project operations
type Handler struct {
	service   *Service
	validator *validator.Validator
	logger    *slog.Logger
}

// NewHandler creates a new project handler
func NewHandler(service *Service, validator *validator.Validator, logger *slog.Logger) *Handler {
	return &Handler{
		service:   service,
		validator: validator,
		logger:    logger,
	}
}

// CreateProject handles POST /api/v1/projects
func (h *Handler) CreateProject(w http.ResponseWriter, r *http.Request) {
	// Get user from context
	claims, ok := auth.GetUserFromContext(r.Context())
	if !ok || claims == nil {
		api.Unauthorized(r.Context(), w, "請先登入")
		return
	}

	// Check permission - only CISA and SPO can create projects
	if claims.Role != constants.UserRoles.CISA && claims.Role != constants.UserRoles.SPO {
		api.Forbidden(r.Context(), w, "權限不足")
		return
	}

	// Parse request body
	var req CreateProjectRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		api.BadRequest(r.Context(), w, "Invalid JSON format")
		return
	}

	// Validate request - use manual validation for now
	if req.Name == "" {
		api.BadRequest(r.Context(), w, "專案名稱不能為空")
		return
	}
	if len(req.Name) > 200 {
		api.BadRequest(r.Context(), w, "專案名稱不能超過200字符")
		return
	}

	// Validate enum values
	if !isValidProjectType(req.Type) {
		api.BadRequest(r.Context(), w, "無效的專案類型")
		return
	}

	if !isValidProjectCategory(req.Category) {
		api.BadRequest(r.Context(), w, "無效的專案分類")
		return
	}

	if req.Status != "" && !isValidProjectStatus(req.Status) {
		api.BadRequest(r.Context(), w, "無效的專案狀態")
		return
	}

	// Create a project
	project, err := h.service.CreateProject(r.Context(), req, claims.UserID)
	if err != nil {
		h.logger.ErrorContext(r.Context(), "Failed to create project", slog.String("error", err.Error()))
		errMsg := api.DBErrorMessage(err, "建立專案")
		api.InternalError(r.Context(), w, errMsg)
		return
	}

	h.logger.InfoContext(r.Context(), "Project created successfully",
		slog.Int("project_id", int(project.ID)),
		slog.String("project_name", project.Name),
		slog.Int("user_id", int(claims.UserID)))

	api.Success(r.Context(), w, project)
}

// GetProject handles GET /api/v1/projects/{id}
func (h *Handler) GetProject(w http.ResponseWriter, r *http.Request) {
	// Get user from context
	claims, ok := auth.GetUserFromContext(r.Context())
	if !ok || claims == nil {
		api.Unauthorized(r.Context(), w, "請先登入")
		return
	}

	// Get project ID from URL
	projectIDStr := r.PathValue("id")
	projectID, err := strconv.ParseInt(projectIDStr, 10, 32)
	if err != nil {
		api.BadRequest(r.Context(), w, "無效的專案ID")
		return
	}

	// Get project
	project, err := h.service.GetProjectByID(r.Context(), int32(projectID))
	if err != nil {
		if errors.Is(err, ErrProjectNotFound) {
			api.NotFound(r.Context(), w, "專案不存在")
			return
		}
		h.logger.ErrorContext(r.Context(), "Failed to get project", slog.String("error", err.Error()))
		errMsg := api.DBErrorMessage(err, "查詢專案")
		api.InternalError(r.Context(), w, errMsg)
		return
	}

	api.Success(r.Context(), w, project)
}

// UpdateProject handles PUT /api/v1/projects/{id}
func (h *Handler) UpdateProject(w http.ResponseWriter, r *http.Request) {
	// Get user from context
	claims, ok := auth.GetUserFromContext(r.Context())
	if !ok || claims == nil {
		api.Unauthorized(w, "請先登入")
		return
	}

	// Check permission - only CISA and SPO can update projects
	if claims.Role != constants.UserRoles.CISA && claims.Role != constants.UserRoles.SPO {
		api.Forbidden(w, "權限不足")
		return
	}

	// Get project ID from URL
	projectIDStr := r.PathValue("id")
	projectID, err := strconv.ParseInt(projectIDStr, 10, 32)
	if err != nil {
		api.BadRequest(w, "無效的專案ID")
		return
	}

	// Parse request body
	var req UpdateProjectRequest
	if err = json.NewDecoder(r.Body).Decode(&req); err != nil {
		api.BadRequest(w, "Invalid JSON format")
		return
	}

	// Validate request - use manual validation for now
	if req.Name == "" {
		api.BadRequest(w, "專案名稱不能為空")
		return
	}
	if len(req.Name) > 200 {
		api.BadRequest(w, "專案名稱不能超過200字符")
		return
	}

	// Validate enum values
	if !isValidProjectType(req.Type) {
		api.BadRequest(w, "無效的專案類型")
		return
	}

	if !isValidProjectCategory(req.Category) {
		api.BadRequest(w, "無效的專案分類")
		return
	}

	if !isValidProjectStatus(req.Status) {
		api.BadRequest(w, "無效的專案狀態")
		return
	}

	// Update project
	project, err := h.service.UpdateProject(r.Context(), int32(projectID), req, claims.UserID)
	if err != nil {
		if errors.Is(err, ErrProjectNotFound) {
			api.NotFound(w, "專案不存在")
			return
		}
		h.logger.ErrorContext(r.Context(), "Failed to update project", slog.String("error", err.Error()))
		errMsg := api.DBErrorMessage(err, "更新專案")
		api.InternalError(r.Context(), w, errMsg)
		return
	}

	h.logger.InfoContext(r.Context(), "Project updated successfully",
		slog.Int("project_id", int(project.ID)),
		slog.String("project_name", project.Name),
		slog.Int("user_id", int(claims.UserID)))

	api.Success(r.Context(), w, project)
}

// DeleteProject handles DELETE /api/v1/projects/{id}
func (h *Handler) DeleteProject(w http.ResponseWriter, r *http.Request) {
	// Get user from context
	claims, ok := auth.GetUserFromContext(r.Context())
	if !ok || claims == nil {
		api.Unauthorized(w, "請先登入")
		return
	}

	// Check permission - only CISA and SPO can delete projects
	if claims.Role != constants.UserRoles.CISA && claims.Role != constants.UserRoles.SPO {
		api.Forbidden(w, "權限不足")
		return
	}

	// Get project ID from URL
	projectIDStr := r.PathValue("id")
	projectID, err := strconv.ParseInt(projectIDStr, 10, 32)
	if err != nil {
		api.BadRequest(w, "無效的專案ID")
		return
	}

	// Delete project
	if err = h.service.DeleteProject(r.Context(), int32(projectID), claims.UserID); err != nil {
		if errors.Is(err, ErrProjectNotFound) {
			api.NotFound(w, "專案不存在")
			return
		}
		h.logger.ErrorContext(r.Context(), "Failed to delete project", slog.String("error", err.Error()))
		errMsg := api.DBErrorMessage(err, "刪除專案")
		api.InternalError(r.Context(), w, errMsg)
		return
	}

	h.logger.InfoContext(r.Context(), "Project deleted successfully",
		slog.Int("project_id", int(projectID)),
		slog.Int("user_id", int(claims.UserID)))

	api.Success(r.Context(), w, map[string]string{"message": "專案刪除成功"})
}

// ListProjects handles GET /api/v1/projects
func (h *Handler) ListProjects(w http.ResponseWriter, r *http.Request) {
	// Get user from context
	claims, ok := auth.GetUserFromContext(r.Context())
	if !ok || claims == nil {
		api.Unauthorized(w, "請先登入")
		return
	}

	// Parse query parameters
	var req ListProjectsRequest

	// Parse pagination
	pagination := api.ParseRequestPagination(r)
	req.Pagination = pagination

	// Parse filters
	if typeStr := r.URL.Query().Get("type"); typeStr != "" {
		projectType := sqlc.ProjectType(typeStr)
		if !isValidProjectType(projectType) {
			api.BadRequest(w, "無效的專案類型")
			return
		}
		req.Type = &projectType
	}

	if categoryStr := r.URL.Query().Get("category"); categoryStr != "" {
		projectCategory := sqlc.ProjectCategory(categoryStr)
		if !isValidProjectCategory(projectCategory) {
			api.BadRequest(w, "無效的專案分類")
			return
		}
		req.Category = &projectCategory
	}

	if statusStr := r.URL.Query().Get("status"); statusStr != "" {
		projectStatus := sqlc.ProjectStatus(statusStr)
		if !isValidProjectStatus(projectStatus) {
			api.BadRequest(w, "無效的專案狀態")
			return
		}
		req.Status = &projectStatus
	}

	if isTestStr := r.URL.Query().Get("is_test"); isTestStr != "" {
		isTest, err := strconv.ParseBool(isTestStr)
		if err != nil {
			api.BadRequest(w, "無效的測試專案參數")
			return
		}
		req.IsTest = &isTest
	}

	req.Search = r.URL.Query().Get("search")

	// List projects
	response, err := h.service.ListProjects(r.Context(), req)
	if err != nil {
		h.logger.ErrorContext(r.Context(), "Failed to list projects", slog.String("error", err.Error()))
		errMsg := api.DBErrorMessage(err, "查詢專案清單")
		api.InternalError(r.Context(), w, errMsg)
		return
	}

	api.Success(r.Context(), w, response)
}

// Validation helper functions
func isValidProjectType(projectType sqlc.ProjectType) bool {
	validTypes := []sqlc.ProjectType{
		constants.ProjectTypes.General,  // "一般詢價"
		constants.ProjectTypes.Periodic, // "定期詢價"
	}

	for _, validType := range validTypes {
		if projectType == validType {
			return true
		}
	}
	return false
}

func isValidProjectCategory(category sqlc.ProjectCategory) bool {
	validCategories := []sqlc.ProjectCategory{
		constants.ProjectCategories.Hardware, // "電腦軟體雲端服務"
		constants.ProjectCategories.Software, // "資訊服務"
	}

	for _, validCategory := range validCategories {
		if category == validCategory {
			return true
		}
	}
	return false
}

func isValidProjectStatus(status sqlc.ProjectStatus) bool {
	validStatuses := []sqlc.ProjectStatus{
		constants.ProjectStatuses.Active, // "進行中"
		constants.ProjectStatuses.Closed, // "關閉"
	}

	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}

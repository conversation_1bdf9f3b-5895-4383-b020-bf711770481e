// Package quote 提供報價管理相關的業務邏輯
// 負責處理報價的新增、修改、刪除、查詢及審核等核心功能
package quote

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"log/slog"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/koopa0/pms-api-v2/internal/constants"
	"github.com/koopa0/pms-api-v2/internal/platform/database"
	"github.com/koopa0/pms-api-v2/internal/types"
	"github.com/koopa0/pms-api-v2/sqlc"
)

// Service 報價服務層
type Service struct {
	db     *database.DB
	logger *slog.Logger
}

// NewService 創建報價服務實例
func NewService(db *database.DB, logger *slog.Logger) *Service {
	return &Service{
		db:     db,
		logger: logger,
	}
}

// 通用錯誤定義
var (
	ErrQuoteNotFound    = errors.New("報價不存在")
	ErrDuplicateQuote   = errors.New("該產品已存在報價")
	ErrInvalidQuoteType = errors.New("無效的報價類型")
	ErrInvalidStatus    = errors.New("無效的報價狀態")
	ErrUnauthorized     = errors.New("無權限執行此操作")
	ErrInvalidInput     = errors.New("輸入參數無效")
	ErrProjectNotActive = errors.New("專案未在進行中")
	ErrProductNotFound  = errors.New("產品不存在")
	ErrAlreadyReviewed  = errors.New("報價已被審核")
)

// CreateQuoteRequest 創建報價請求
type CreateQuoteRequest struct {
	ProjectID      int32          `json:"project_id"`
	ProductID      int32          `json:"product_id"`
	QuoteType      sqlc.QuoteType `json:"quote_type"`
	MarketPrice    *float64       `json:"market_price"`
	InternetPrice  *float64       `json:"internet_price"`
	OriginalPrice  *float64       `json:"original_price"`
	PromotionPrice *float64       `json:"promotion_price"`
	BidPrice       *float64       `json:"bid_price"`
	SameAsBidPrice bool           `json:"same_as_bid_price"`
	Remark         string         `json:"remark"`
}

// UpdateQuoteRequest 更新報價請求
type UpdateQuoteRequest struct {
	MarketPrice    *float64 `json:"market_price"`
	InternetPrice  *float64 `json:"internet_price"`
	OriginalPrice  *float64 `json:"original_price"`
	PromotionPrice *float64 `json:"promotion_price"`
	BidPrice       *float64 `json:"bid_price"`
	SameAsBidPrice bool     `json:"same_as_bid_price"`
	Remark         string   `json:"remark"`
}

// ReviewQuoteRequest 審核報價請求
type ReviewQuoteRequest struct {
	Status      sqlc.QuoteStatus `json:"status"`
	AdminRemark string           `json:"admin_remark"`
}

// ListQuotesRequest 列出報價請求
type ListQuotesRequest struct {
	ProjectID  *int32            `json:"project_id"`
	ProductID  *int32            `json:"product_id"`
	UserID     *int32            `json:"user_id"`
	QuoteType  *sqlc.QuoteType   `json:"quote_type"`
	Status     *sqlc.QuoteStatus `json:"status"`
	BatchID    *int32            `json:"batch_id"`
	Pagination types.PaginationParams
}

// ListQuotesResponse 列出報價回應
type ListQuotesResponse struct {
	Data       []Details            `json:"data"`
	Pagination types.PaginationMeta `json:"pagination"`
}

// Info 報價基本資訊
type Info struct {
	ID             int32             `json:"id"`
	ProjectID      int32             `json:"project_id"`
	ProductID      int32             `json:"product_id"`
	UserID         int32             `json:"user_id"`
	QuoteType      sqlc.QuoteType    `json:"quote_type"`
	MarketPrice    *float64          `json:"market_price"`
	InternetPrice  *float64          `json:"internet_price"`
	OriginalPrice  *float64          `json:"original_price"`
	PromotionPrice *float64          `json:"promotion_price"`
	BidPrice       *float64          `json:"bid_price"`
	SameAsBidPrice bool              `json:"same_as_bid_price"`
	Status         sqlc.QuoteStatus  `json:"status"`
	Remark         string            `json:"remark"`
	AdminRemark    string            `json:"admin_remark"`
	CreatedAt      pgtype.Timestamp  `json:"created_at"`
	UpdatedAt      pgtype.Timestamp  `json:"updated_at"`
	ReviewedAt     *pgtype.Timestamp `json:"reviewed_at"`
	ReviewedBy     *int32            `json:"reviewed_by"`
	BatchID        *int32            `json:"batch_id"`
}

// Details 包含詳細資訊的報價
type Details struct {
	Info
	ProjectName        string  `json:"project_name"`
	ProductName        string  `json:"product_name"`
	ProductPID         string  `json:"product_pid"`
	UserUsername       string  `json:"user_username"`
	UserEmail          *string `json:"user_email,omitempty"`
	ReviewedByUsername *string `json:"reviewed_by_username,omitempty"`
}

// CreateQuote 創建新報價
func (s *Service) CreateQuote(ctx context.Context, req CreateQuoteRequest, userID int32) (*Info, error) {
	// 驗證專案狀態
	project, err := s.db.Queries.GetProjectByID(ctx, req.ProjectID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrProjectNotActive
		}
		return nil, database.WrapError(err, "get project")
	}

	if project.Status != constants.ProjectStatuses.Active {
		return nil, ErrProjectNotActive
	}

	// 驗證產品存在
	_, err = s.db.Queries.GetProductByID(ctx, req.ProductID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrProductNotFound
		}
		return nil, database.WrapError(err, "get product")
	}

	// 檢查是否已存在報價
	existingID, err := s.db.Queries.CheckQuoteExists(ctx, sqlc.CheckQuoteExistsParams{
		UserID:    userID,
		ProjectID: req.ProjectID,
		ProductID: req.ProductID,
	})
	if err == nil && existingID > 0 {
		return nil, ErrDuplicateQuote
	}

	// 創建報價
	quote, err := s.db.Queries.CreateQuote(ctx, sqlc.CreateQuoteParams{
		ProjectID:      req.ProjectID,
		ProductID:      req.ProductID,
		UserID:         userID,
		QuoteType:      req.QuoteType,
		MarketPrice:    types.Float64ToNumeric(req.MarketPrice),
		InternetPrice:  types.Float64ToNumeric(req.InternetPrice),
		OriginalPrice:  types.Float64ToNumeric(req.OriginalPrice),
		PromotionPrice: types.Float64ToNumeric(req.PromotionPrice),
		BidPrice:       types.Float64ToNumeric(req.BidPrice),
		SameAsBidPrice: pgtype.Bool{Bool: req.SameAsBidPrice, Valid: true},
		Status:         constants.QuoteStatuses.Pending,
		Remark:         pgtype.Text{String: req.Remark, Valid: req.Remark != ""},
		AdminRemark:    pgtype.Text{Valid: false},
		BatchID:        pgtype.Int4{Valid: false},
	})
	if err != nil {
		return nil, database.WrapError(err, "create quote")
	}

	// 記錄系統日誌
	logParams := map[string]interface{}{
		"quote_id":   quote.ID,
		"project_id": req.ProjectID,
		"product_id": req.ProductID,
		"user_id":    userID,
		"quote_type": req.QuoteType,
	}
	s.logger.InfoContext(ctx, "quote created", slog.Any("params", logParams))

	return s.convertToQuoteInfo(quote), nil
}

// GetQuoteByID 根據 ID 獲取報價
func (s *Service) GetQuoteByID(ctx context.Context, quoteID int32) (*Info, error) {
	quote, err := s.db.Queries.GetQuoteByID(ctx, quoteID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrQuoteNotFound
		}
		return nil, database.WrapError(err, "get quote")
	}

	return s.convertToQuoteInfo(quote), nil
}

// GetQuoteWithDetails 獲取報價詳細資訊
func (s *Service) GetQuoteWithDetails(ctx context.Context, quoteID int32) (*Details, error) {
	result, err := s.db.Queries.GetQuoteByIDWithDetails(ctx, quoteID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrQuoteNotFound
		}
		return nil, database.WrapError(err, "get quote details")
	}

	return s.convertToQuoteWithDetails(result), nil
}

// UpdateQuote 更新報價
func (s *Service) UpdateQuote(ctx context.Context, quoteID int32, req UpdateQuoteRequest, userID int32) (*Info, error) {
	// 獲取現有報價
	existing, err := s.db.Queries.GetQuoteByID(ctx, quoteID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrQuoteNotFound
		}
		return nil, database.WrapError(err, "get quote")
	}

	// 檢查權限
	if existing.UserID != userID {
		return nil, ErrUnauthorized
	}

	// 檢查狀態 - 只有待審和退件狀態可以修改
	if existing.Status != constants.QuoteStatuses.Pending && existing.Status != constants.QuoteStatuses.Rejected {
		return nil, errors.New("只有待審或退件狀態的報價可以修改")
	}

	// 更新報價
	quote, err := s.db.Queries.UpdateQuote(ctx, sqlc.UpdateQuoteParams{
		ID:             quoteID,
		QuoteType:      existing.QuoteType, // 保持原有類型
		MarketPrice:    types.Float64ToNumeric(req.MarketPrice),
		InternetPrice:  types.Float64ToNumeric(req.InternetPrice),
		OriginalPrice:  types.Float64ToNumeric(req.OriginalPrice),
		PromotionPrice: types.Float64ToNumeric(req.PromotionPrice),
		BidPrice:       types.Float64ToNumeric(req.BidPrice),
		SameAsBidPrice: pgtype.Bool{Bool: req.SameAsBidPrice, Valid: true},
		Status:         constants.QuoteStatuses.Resent, // 重送狀態
		Remark:         pgtype.Text{String: req.Remark, Valid: req.Remark != ""},
		AdminRemark:    existing.AdminRemark,
		BatchID:        existing.BatchID,
	})
	if err != nil {
		return nil, database.WrapError(err, "update quote")
	}

	s.logger.InfoContext(ctx, "quote updated",
		slog.Int("quote_id", int(quoteID)),
		slog.Int("user_id", int(userID)))

	return s.convertToQuoteInfo(quote), nil
}

// DeleteQuote 刪除報價（軟刪除）
func (s *Service) DeleteQuote(ctx context.Context, quoteID int32, userID int32) error {
	// 獲取報價檢查權限
	quote, err := s.db.Queries.GetQuoteByID(ctx, quoteID)
	if err != nil {
		if err == sql.ErrNoRows {
			return ErrQuoteNotFound
		}
		return database.WrapError(err, "get quote")
	}

	// 檢查權限
	if quote.UserID != userID {
		return ErrUnauthorized
	}

	// 執行軟刪除
	err = s.db.Queries.DeleteQuote(ctx, quoteID)
	if err != nil {
		return database.WrapError(err, "delete quote")
	}

	s.logger.InfoContext(ctx, "quote deleted",
		slog.Int("quote_id", int(quoteID)),
		slog.Int("user_id", int(userID)))

	return nil
}

// ReviewQuote 審核報價（管理員功能）
func (s *Service) ReviewQuote(ctx context.Context, quoteID int32, req ReviewQuoteRequest, reviewerID int32) (*Info, error) {
	// 獲取報價
	existing, err := s.db.Queries.GetQuoteByID(ctx, quoteID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrQuoteNotFound
		}
		return nil, database.WrapError(err, "get quote")
	}

	// 檢查是否已審核
	if existing.Status == constants.QuoteStatuses.Approved || existing.ReviewedBy.Valid {
		return nil, ErrAlreadyReviewed
	}

	// 驗證狀態
	if req.Status != constants.QuoteStatuses.Approved && req.Status != constants.QuoteStatuses.Rejected {
		return nil, ErrInvalidStatus
	}

	// 更新狀態
	quote, err := s.db.Queries.UpdateQuoteStatus(ctx, sqlc.UpdateQuoteStatusParams{
		ID:          quoteID,
		Status:      req.Status,
		AdminRemark: pgtype.Text{String: req.AdminRemark, Valid: req.AdminRemark != ""},
		ReviewedBy:  pgtype.Int4{Int32: reviewerID, Valid: true},
	})
	if err != nil {
		return nil, database.WrapError(err, "update quote status")
	}

	s.logger.InfoContext(ctx, "quote reviewed",
		slog.Int("quote_id", int(quoteID)),
		slog.String("status", string(req.Status)),
		slog.Int("reviewer_id", int(reviewerID)))

	return s.convertToQuoteInfo(quote), nil
}

// ListQuotes 列出報價
func (s *Service) ListQuotes(ctx context.Context, req ListQuotesRequest) (*ListQuotesResponse, error) {
	// 計算總數
	count, err := s.db.Queries.CountQuotes(ctx, sqlc.CountQuotesParams{
		ProjectID: types.Int32PtrToPgTypeInt4(req.ProjectID),
		ProductID: types.Int32PtrToPgTypeInt4(req.ProductID),
		UserID:    types.Int32PtrToPgTypeInt4(req.UserID),
		QuoteType: s.quoteTypeToNullable(req.QuoteType),
		Status:    s.quoteStatusToNullable(req.Status),
		BatchID:   types.Int32PtrToPgTypeInt4(req.BatchID),
	})
	if err != nil {
		return nil, database.WrapError(err, "count quotes")
	}

	// 查詢報價列表
	limit, offset := req.Pagination.GetLimitOffset()
	quotes, err := s.db.Queries.ListQuotes(ctx, sqlc.ListQuotesParams{
		ProjectID: types.Int32PtrToPgTypeInt4(req.ProjectID),
		ProductID: types.Int32PtrToPgTypeInt4(req.ProductID),
		UserID:    types.Int32PtrToPgTypeInt4(req.UserID),
		QuoteType: s.quoteTypeToNullable(req.QuoteType),
		Status:    s.quoteStatusToNullable(req.Status),
		BatchID:   types.Int32PtrToPgTypeInt4(req.BatchID),
		Limit:     limit,
		Offset:    offset,
	})
	if err != nil {
		return nil, database.WrapError(err, "list quotes")
	}

	// 轉換結果
	data := make([]Details, 0, len(quotes))
	for _, q := range quotes {
		data = append(data, *s.convertListRowToQuoteWithDetails(q))
	}

	return &ListQuotesResponse{
		Data:       data,
		Pagination: req.Pagination.GetMeta(int(count)),
	}, nil
}

// GetQuotesByProject 獲取專案的所有報價
func (s *Service) GetQuotesByProject(ctx context.Context, projectID int32) ([]Details, error) {
	quotes, err := s.db.Queries.GetQuotesByProject(ctx, projectID)
	if err != nil {
		return nil, database.WrapError(err, "get project quotes")
	}

	result := make([]Details, 0, len(quotes))
	for _, q := range quotes {
		result = append(result, *s.convertProjectQuoteToQuoteWithDetails(q))
	}

	return result, nil
}

// GetQuotesByUser 獲取用戶的報價列表
func (s *Service) GetQuotesByUser(ctx context.Context, userID int32, pagination types.PaginationParams) (*ListQuotesResponse, error) {
	limit, offset := pagination.GetLimitOffset()

	// 查詢用戶報價
	quotes, err := s.db.Queries.GetQuotesByUser(ctx, sqlc.GetQuotesByUserParams{
		UserID: userID,
		Limit:  limit,
		Offset: offset,
	})
	if err != nil {
		return nil, database.WrapError(err, "get user quotes")
	}

	// 計算總數（這裡簡化處理，實際應該有專門的計數查詢）
	count := len(quotes)
	if len(quotes) == int(limit) {
		count = int(offset) + len(quotes) + 1 // 估算
	}

	result := make([]Details, 0, len(quotes))
	for _, q := range quotes {
		result = append(result, *s.convertUserQuoteToQuoteWithDetails(q))
	}

	return &ListQuotesResponse{
		Data:       result,
		Pagination: pagination.GetMeta(count),
	}, nil
}

// GetPendingQuotes 獲取待審核報價
func (s *Service) GetPendingQuotes(ctx context.Context) ([]Details, error) {
	quotes, err := s.db.Queries.GetPendingQuotes(ctx)
	if err != nil {
		return nil, database.WrapError(err, "get pending quotes")
	}

	result := make([]Details, 0, len(quotes))
	for _, q := range quotes {
		result = append(result, *s.convertPendingQuoteToQuoteWithDetails(q))
	}

	return result, nil
}

// BatchReviewQuotes 批量審核報價
func (s *Service) BatchReviewQuotes(ctx context.Context, quoteIDs []int32, status sqlc.QuoteStatus, adminRemark string, reviewerID int32) error {
	// 驗證狀態
	if status != constants.QuoteStatuses.Approved && status != constants.QuoteStatuses.Rejected {
		return ErrInvalidStatus
	}

	// 生成批次ID
	batchID := int32(time.Now().UnixMilli())

	// 執行批量更新
	err := s.db.Queries.BatchUpdateQuoteStatus(ctx, sqlc.BatchUpdateQuoteStatusParams{
		Status:      status,
		AdminRemark: pgtype.Text{String: adminRemark, Valid: adminRemark != ""},
		ReviewedBy:  pgtype.Int4{Int32: reviewerID, Valid: true},
		BatchID:     pgtype.Int4{Int32: batchID, Valid: true},
		QuoteIds:    quoteIDs,
	})
	if err != nil {
		return database.WrapError(err, "batch update quotes")
	}

	s.logger.InfoContext(ctx, "quotes batch reviewed",
		slog.Int("count", len(quoteIDs)),
		slog.String("status", string(status)),
		slog.Int("batch_id", int(batchID)),
		slog.Int("reviewer_id", int(reviewerID)))

	return nil
}

// 輔助方法：轉換報價資訊
func (s *Service) convertToQuoteInfo(q sqlc.Quote) *Info {
	info := &Info{
		ID:             q.ID,
		ProjectID:      q.ProjectID,
		ProductID:      q.ProductID,
		UserID:         q.UserID,
		QuoteType:      q.QuoteType,
		MarketPrice:    types.NumericToFloat64(q.MarketPrice),
		InternetPrice:  types.NumericToFloat64(q.InternetPrice),
		OriginalPrice:  types.NumericToFloat64(q.OriginalPrice),
		PromotionPrice: types.NumericToFloat64(q.PromotionPrice),
		BidPrice:       types.NumericToFloat64(q.BidPrice),
		SameAsBidPrice: q.SameAsBidPrice.Bool,
		Status:         q.Status,
		Remark:         q.Remark.String,
		AdminRemark:    q.AdminRemark.String,
		CreatedAt:      q.CreatedAt,
		UpdatedAt:      q.UpdatedAt,
	}

	if q.ReviewedAt.Valid {
		info.ReviewedAt = &q.ReviewedAt
	}
	if q.ReviewedBy.Valid {
		info.ReviewedBy = &q.ReviewedBy.Int32
	}
	if q.BatchID.Valid {
		info.BatchID = &q.BatchID.Int32
	}

	return info
}

// convertToQuoteWithDetails 轉換為包含詳細資訊的報價
func (s *Service) convertToQuoteWithDetails(row sqlc.GetQuoteByIDWithDetailsRow) *Details {
	// Create a Quote from the row
	quote := sqlc.Quote{
		ID:             row.ID,
		ProjectID:      row.ProjectID,
		ProductID:      row.ProductID,
		UserID:         row.UserID,
		QuoteType:      row.QuoteType,
		MarketPrice:    row.MarketPrice,
		InternetPrice:  row.InternetPrice,
		OriginalPrice:  row.OriginalPrice,
		PromotionPrice: row.PromotionPrice,
		BidPrice:       row.BidPrice,
		SameAsBidPrice: row.SameAsBidPrice,
		Status:         row.Status,
		Remark:         row.Remark,
		AdminRemark:    row.AdminRemark,
		IsDeleted:      row.IsDeleted,
		CreatedAt:      row.CreatedAt,
		UpdatedAt:      row.UpdatedAt,
		ReviewedAt:     row.ReviewedAt,
		ReviewedBy:     row.ReviewedBy,
		BatchID:        row.BatchID,
	}

	details := &Details{
		Info:         *s.convertToQuoteInfo(quote),
		ProjectName:  row.ProjectName.String,
		ProductName:  row.ProductName.String,
		ProductPID:   fmt.Sprintf("%d", row.ProductPid.Int32),
		UserUsername: row.UserUsername.String,
	}

	if row.UserEmail.Valid {
		details.UserEmail = &row.UserEmail.String
	}
	if row.ReviewedByUsername.Valid {
		details.ReviewedByUsername = &row.ReviewedByUsername.String
	}

	return details
}

// convertListRowToQuoteWithDetails 轉換列表查詢結果
func (s *Service) convertListRowToQuoteWithDetails(row sqlc.ListQuotesRow) *Details {
	// Create a Quote from the row
	quote := sqlc.Quote{
		ID:             row.ID,
		ProjectID:      row.ProjectID,
		ProductID:      row.ProductID,
		UserID:         row.UserID,
		QuoteType:      row.QuoteType,
		MarketPrice:    row.MarketPrice,
		InternetPrice:  row.InternetPrice,
		OriginalPrice:  row.OriginalPrice,
		PromotionPrice: row.PromotionPrice,
		BidPrice:       row.BidPrice,
		SameAsBidPrice: row.SameAsBidPrice,
		Status:         row.Status,
		Remark:         row.Remark,
		AdminRemark:    row.AdminRemark,
		IsDeleted:      row.IsDeleted,
		CreatedAt:      row.CreatedAt,
		UpdatedAt:      row.UpdatedAt,
		ReviewedAt:     row.ReviewedAt,
		ReviewedBy:     row.ReviewedBy,
		BatchID:        row.BatchID,
	}

	details := &Details{
		Info:         *s.convertToQuoteInfo(quote),
		ProjectName:  row.ProjectName.String,
		ProductName:  row.ProductName.String,
		ProductPID:   fmt.Sprintf("%d", row.ProductPid.Int32),
		UserUsername: row.UserUsername.String,
	}

	if row.ReviewedByUsername.Valid {
		details.ReviewedByUsername = &row.ReviewedByUsername.String
	}

	return details
}

// convertProjectQuoteToQuoteWithDetails 轉換專案報價查詢結果
func (s *Service) convertProjectQuoteToQuoteWithDetails(row sqlc.GetQuotesByProjectRow) *Details {
	// Create a Quote from the row
	quote := sqlc.Quote{
		ID:             row.ID,
		ProjectID:      row.ProjectID,
		ProductID:      row.ProductID,
		UserID:         row.UserID,
		QuoteType:      row.QuoteType,
		MarketPrice:    row.MarketPrice,
		InternetPrice:  row.InternetPrice,
		OriginalPrice:  row.OriginalPrice,
		PromotionPrice: row.PromotionPrice,
		BidPrice:       row.BidPrice,
		SameAsBidPrice: row.SameAsBidPrice,
		Status:         row.Status,
		Remark:         row.Remark,
		AdminRemark:    row.AdminRemark,
		IsDeleted:      row.IsDeleted,
		CreatedAt:      row.CreatedAt,
		UpdatedAt:      row.UpdatedAt,
		ReviewedAt:     row.ReviewedAt,
		ReviewedBy:     row.ReviewedBy,
		BatchID:        row.BatchID,
	}

	return &Details{
		Info:         *s.convertToQuoteInfo(quote),
		ProductName:  row.ProductName.String,
		ProductPID:   fmt.Sprintf("%d", row.ProductPid.Int32),
		UserUsername: row.UserUsername.String,
	}
}

// quoteTypeToNullable 轉換報價類型為可空類型
func (s *Service) quoteTypeToNullable(qt *sqlc.QuoteType) sqlc.NullQuoteType {
	if qt == nil {
		return sqlc.NullQuoteType{Valid: false}
	}
	return sqlc.NullQuoteType{QuoteType: *qt, Valid: true}
}

// quoteStatusToNullable 轉換報價狀態為可空類型
func (s *Service) quoteStatusToNullable(qs *sqlc.QuoteStatus) sqlc.NullQuoteStatus {
	if qs == nil {
		return sqlc.NullQuoteStatus{Valid: false}
	}
	return sqlc.NullQuoteStatus{QuoteStatus: *qs, Valid: true}
}

// convertUserQuoteToQuoteWithDetails 轉換用戶報價查詢結果
func (s *Service) convertUserQuoteToQuoteWithDetails(row sqlc.GetQuotesByUserRow) *Details {
	// Create a Quote from the row
	quote := sqlc.Quote{
		ID:             row.ID,
		ProjectID:      row.ProjectID,
		ProductID:      row.ProductID,
		UserID:         row.UserID,
		QuoteType:      row.QuoteType,
		MarketPrice:    row.MarketPrice,
		InternetPrice:  row.InternetPrice,
		OriginalPrice:  row.OriginalPrice,
		PromotionPrice: row.PromotionPrice,
		BidPrice:       row.BidPrice,
		SameAsBidPrice: row.SameAsBidPrice,
		Status:         row.Status,
		Remark:         row.Remark,
		AdminRemark:    row.AdminRemark,
		IsDeleted:      row.IsDeleted,
		CreatedAt:      row.CreatedAt,
		UpdatedAt:      row.UpdatedAt,
		ReviewedAt:     row.ReviewedAt,
		ReviewedBy:     row.ReviewedBy,
		BatchID:        row.BatchID,
	}

	return &Details{
		Info:        *s.convertToQuoteInfo(quote),
		ProjectName: row.ProjectName.String,
		ProductName: row.ProductName.String,
		ProductPID:  fmt.Sprintf("%d", row.ProductPid.Int32),
	}
}

// convertPendingQuoteToQuoteWithDetails 轉換待審核報價查詢結果
func (s *Service) convertPendingQuoteToQuoteWithDetails(row sqlc.GetPendingQuotesRow) *Details {
	// Create a Quote from the row
	quote := sqlc.Quote{
		ID:             row.ID,
		ProjectID:      row.ProjectID,
		ProductID:      row.ProductID,
		UserID:         row.UserID,
		QuoteType:      row.QuoteType,
		MarketPrice:    row.MarketPrice,
		InternetPrice:  row.InternetPrice,
		OriginalPrice:  row.OriginalPrice,
		PromotionPrice: row.PromotionPrice,
		BidPrice:       row.BidPrice,
		SameAsBidPrice: row.SameAsBidPrice,
		Status:         row.Status,
		Remark:         row.Remark,
		AdminRemark:    row.AdminRemark,
		IsDeleted:      row.IsDeleted,
		CreatedAt:      row.CreatedAt,
		UpdatedAt:      row.UpdatedAt,
		ReviewedAt:     row.ReviewedAt,
		ReviewedBy:     row.ReviewedBy,
		BatchID:        row.BatchID,
	}

	details := &Details{
		Info:         *s.convertToQuoteInfo(quote),
		ProjectName:  row.ProjectName.String,
		ProductName:  row.ProductName.String,
		ProductPID:   fmt.Sprintf("%d", row.ProductPid.Int32),
		UserUsername: row.UserUsername.String,
	}

	if row.UserEmail.Valid {
		details.UserEmail = &row.UserEmail.String
	}

	return details
}

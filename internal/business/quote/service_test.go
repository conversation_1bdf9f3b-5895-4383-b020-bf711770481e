package quote

import (
	"context"
	"database/sql"
	"errors"
	"log/slog"
	"os"
	"testing"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/koopa0/pms-api-v2/internal/constants"
	"github.com/koopa0/pms-api-v2/internal/platform/database"
	"github.com/koopa0/pms-api-v2/internal/types"
	"github.com/koopa0/pms-api-v2/sqlc"
)

// MockDB implements a mock database for testing
type MockDB struct {
	queries *MockQueries
}

// Queries returns the mock queries
func (m *MockDB) Queries() *sqlc.Queries {
	// Create a sqlc.Queries instance with our mock as the underlying db
	return sqlc.New(m)
}

// Implement the DBTX interface methods that sqlc.Queries expects
func (m *MockDB) Exec(ctx context.Context, sql string, arguments ...interface{}) (pgconn.CommandTag, error) {
	return pgconn.CommandTag{}, nil
}

func (m *MockDB) Query(ctx context.Context, sql string, args ...interface{}) (pgx.Rows, error) {
	return nil, nil
}

func (m *MockDB) QueryRow(ctx context.Context, sql string, args ...interface{}) pgx.Row {
	return nil
}

func (m *MockDB) CopyFrom(ctx context.Context, tableName pgx.Identifier, columnNames []string, rowSrc pgx.CopyFromSource) (int64, error) {
	return 0, nil
}

// MockQueries implements all the methods we need from sqlc.Querier for testing
type MockQueries struct {
	// Project related
	getProjectByIDResult sqlc.Project
	getProjectByIDError  error

	// Product related
	getProductByIDResult sqlc.Product
	getProductByIDError  error

	// Quote existence check
	checkQuoteExistsResult int32
	checkQuoteExistsError  error

	// Quote CRUD
	createQuoteResult        sqlc.Quote
	createQuoteError         error
	getQuoteByIDResult       sqlc.Quote
	getQuoteByIDError        error
	getQuoteWithDetailsRow   sqlc.GetQuoteByIDWithDetailsRow
	getQuoteWithDetailsError error
	updateQuoteResult        sqlc.Quote
	updateQuoteError         error
	deleteQuoteError         error
	updateQuoteStatusResult  sqlc.Quote
	updateQuoteStatusError   error

	// Quote listing
	countQuotesResult       int64
	countQuotesError        error
	listQuotesResult        []sqlc.ListQuotesRow
	listQuotesError         error
	getQuotesByProjectRows  []sqlc.GetQuotesByProjectRow
	getQuotesByProjectError error
	getQuotesByUserRows     []sqlc.GetQuotesByUserRow
	getQuotesByUserError    error
	getPendingQuotesRows    []sqlc.GetPendingQuotesRow
	getPendingQuotesError   error

	// Batch operations
	batchUpdateError error
}

// Helper function to create test service with mocks
func createTestService() (*Service, *MockQueries) {
	mockQueries := &MockQueries{
		getProjectByIDResult: sqlc.Project{
			ID:     1,
			Name:   "Test Project",
			Status: constants.ProjectStatuses.Active,
		},
		getProductByIDResult: sqlc.Product{
			ID:   1,
			Name: "Test Product",
		},
		createQuoteResult: sqlc.Quote{
			ID:             1,
			ProjectID:      1,
			ProductID:      1,
			UserID:         1,
			QuoteType:      constants.QuoteTypes.Vendor,
			Status:         constants.QuoteStatuses.Pending,
			SameAsBidPrice: pgtype.Bool{Bool: false, Valid: true},
			CreatedAt:      pgtype.Timestamp{Time: time.Now(), Valid: true},
			UpdatedAt:      pgtype.Timestamp{Time: time.Now(), Valid: true},
		},
		getQuoteByIDResult: sqlc.Quote{
			ID:             1,
			ProjectID:      1,
			ProductID:      1,
			UserID:         1,
			QuoteType:      constants.QuoteTypes.Vendor,
			Status:         constants.QuoteStatuses.Pending,
			MarketPrice:    pgtype.Numeric{Valid: true},
			SameAsBidPrice: pgtype.Bool{Bool: false, Valid: true},
			CreatedAt:      pgtype.Timestamp{Time: time.Now(), Valid: true},
			UpdatedAt:      pgtype.Timestamp{Time: time.Now(), Valid: true},
		},
	}

	logger := slog.New(slog.NewTextHandler(os.Stdout, nil))

	// Create mock database with our mock queries
	_ = &MockDB{queries: mockQueries}

	// Create mock database
	mockDBInstance := &database.DB{}

	// Create service with mock database
	service := &Service{
		db:     mockDBInstance,
		logger: logger,
	}

	return service, mockQueries
}

// Define the query interface to match what the service expects
type queryInterface interface {
	GetProjectByID(ctx context.Context, id int32) (sqlc.Project, error)
	GetProductByID(ctx context.Context, id int32) (sqlc.Product, error)
	CheckQuoteExists(ctx context.Context, params sqlc.CheckQuoteExistsParams) (int32, error)
	CreateQuote(ctx context.Context, params sqlc.CreateQuoteParams) (sqlc.Quote, error)
	GetQuoteByID(ctx context.Context, id int32) (sqlc.Quote, error)
	GetQuoteByIDWithDetails(ctx context.Context, id int32) (sqlc.GetQuoteByIDWithDetailsRow, error)
	UpdateQuote(ctx context.Context, params sqlc.UpdateQuoteParams) (sqlc.Quote, error)
	DeleteQuote(ctx context.Context, id int32) error
	UpdateQuoteStatus(ctx context.Context, params sqlc.UpdateQuoteStatusParams) (sqlc.Quote, error)
	CountQuotes(ctx context.Context, params sqlc.CountQuotesParams) (int64, error)
	ListQuotes(ctx context.Context, params sqlc.ListQuotesParams) ([]sqlc.ListQuotesRow, error)
	GetQuotesByProject(ctx context.Context, projectID int32) ([]sqlc.GetQuotesByProjectRow, error)
	GetQuotesByUser(ctx context.Context, params sqlc.GetQuotesByUserParams) ([]sqlc.GetQuotesByUserRow, error)
	GetPendingQuotes(ctx context.Context) ([]sqlc.GetPendingQuotesRow, error)
	BatchUpdateQuoteStatus(ctx context.Context, params sqlc.BatchUpdateQuoteStatusParams) error
}

// Implement the interface methods
func (m *MockQueries) GetProjectByID(ctx context.Context, id int32) (sqlc.Project, error) {
	if m.getProjectByIDError != nil {
		return sqlc.Project{}, m.getProjectByIDError
	}
	return m.getProjectByIDResult, nil
}

func (m *MockQueries) GetProductByID(ctx context.Context, id int32) (sqlc.Product, error) {
	if m.getProductByIDError != nil {
		return sqlc.Product{}, m.getProductByIDError
	}
	return m.getProductByIDResult, nil
}

func (m *MockQueries) CheckQuoteExists(ctx context.Context, params sqlc.CheckQuoteExistsParams) (int32, error) {
	if m.checkQuoteExistsError != nil {
		return 0, m.checkQuoteExistsError
	}
	return m.checkQuoteExistsResult, nil
}

func (m *MockQueries) CreateQuote(ctx context.Context, params sqlc.CreateQuoteParams) (sqlc.Quote, error) {
	if m.createQuoteError != nil {
		return sqlc.Quote{}, m.createQuoteError
	}
	return m.createQuoteResult, nil
}

func (m *MockQueries) GetQuoteByID(ctx context.Context, id int32) (sqlc.Quote, error) {
	if m.getQuoteByIDError != nil {
		return sqlc.Quote{}, m.getQuoteByIDError
	}
	return m.getQuoteByIDResult, nil
}

func (m *MockQueries) GetQuoteByIDWithDetails(ctx context.Context, id int32) (sqlc.GetQuoteByIDWithDetailsRow, error) {
	if m.getQuoteWithDetailsError != nil {
		return sqlc.GetQuoteByIDWithDetailsRow{}, m.getQuoteWithDetailsError
	}
	return m.getQuoteWithDetailsRow, nil
}

func (m *MockQueries) UpdateQuote(ctx context.Context, params sqlc.UpdateQuoteParams) (sqlc.Quote, error) {
	if m.updateQuoteError != nil {
		return sqlc.Quote{}, m.updateQuoteError
	}
	return m.updateQuoteResult, nil
}

func (m *MockQueries) DeleteQuote(ctx context.Context, id int32) error {
	return m.deleteQuoteError
}

func (m *MockQueries) UpdateQuoteStatus(ctx context.Context, params sqlc.UpdateQuoteStatusParams) (sqlc.Quote, error) {
	if m.updateQuoteStatusError != nil {
		return sqlc.Quote{}, m.updateQuoteStatusError
	}
	return m.updateQuoteStatusResult, nil
}

func (m *MockQueries) CountQuotes(ctx context.Context, params sqlc.CountQuotesParams) (int64, error) {
	if m.countQuotesError != nil {
		return 0, m.countQuotesError
	}
	return m.countQuotesResult, nil
}

func (m *MockQueries) ListQuotes(ctx context.Context, params sqlc.ListQuotesParams) ([]sqlc.ListQuotesRow, error) {
	if m.listQuotesError != nil {
		return nil, m.listQuotesError
	}
	return m.listQuotesResult, nil
}

func (m *MockQueries) GetQuotesByProject(ctx context.Context, projectID int32) ([]sqlc.GetQuotesByProjectRow, error) {
	if m.getQuotesByProjectError != nil {
		return nil, m.getQuotesByProjectError
	}
	return m.getQuotesByProjectRows, nil
}

func (m *MockQueries) GetQuotesByUser(ctx context.Context, params sqlc.GetQuotesByUserParams) ([]sqlc.GetQuotesByUserRow, error) {
	if m.getQuotesByUserError != nil {
		return nil, m.getQuotesByUserError
	}
	return m.getQuotesByUserRows, nil
}

func (m *MockQueries) GetPendingQuotes(ctx context.Context) ([]sqlc.GetPendingQuotesRow, error) {
	if m.getPendingQuotesError != nil {
		return nil, m.getPendingQuotesError
	}
	return m.getPendingQuotesRows, nil
}

func (m *MockQueries) BatchUpdateQuoteStatus(ctx context.Context, params sqlc.BatchUpdateQuoteStatusParams) error {
	return m.batchUpdateError
}

func TestService_CreateQuote(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		request        CreateQuoteRequest
		userID         int32
		setupMocks     func(*MockQueries)
		expectedError  error
		validateResult func(*Info) bool
	}{
		{
			name: "successful quote creation",
			request: CreateQuoteRequest{
				ProjectID:     1,
				ProductID:     1,
				QuoteType:     constants.QuoteTypes.Vendor,
				MarketPrice:   floatPtr(1000.0),
				InternetPrice: floatPtr(900.0),
				Remark:        "Test quote",
			},
			userID: 1,
			setupMocks: func(mq *MockQueries) {
				// Default mocks are sufficient
			},
			expectedError: nil,
			validateResult: func(result *Info) bool {
				return result != nil && result.ID == 1
			},
		},
		{
			name: "project not active",
			request: CreateQuoteRequest{
				ProjectID: 1,
				ProductID: 1,
				QuoteType: constants.QuoteTypes.Vendor,
			},
			userID: 1,
			setupMocks: func(mq *MockQueries) {
				mq.getProjectByIDResult.Status = constants.ProjectStatuses.Closed
			},
			expectedError: ErrProjectNotActive,
			validateResult: func(result *Info) bool {
				return result == nil
			},
		},
		{
			name: "project not found",
			request: CreateQuoteRequest{
				ProjectID: 999,
				ProductID: 1,
				QuoteType: constants.QuoteTypes.Vendor,
			},
			userID: 1,
			setupMocks: func(mq *MockQueries) {
				mq.getProjectByIDError = sql.ErrNoRows
			},
			expectedError: ErrProjectNotActive,
			validateResult: func(result *Info) bool {
				return result == nil
			},
		},
		{
			name: "product not found",
			request: CreateQuoteRequest{
				ProjectID: 1,
				ProductID: 999,
				QuoteType: constants.QuoteTypes.Vendor,
			},
			userID: 1,
			setupMocks: func(mq *MockQueries) {
				mq.getProductByIDError = sql.ErrNoRows
			},
			expectedError: ErrProductNotFound,
			validateResult: func(result *Info) bool {
				return result == nil
			},
		},
		{
			name: "duplicate quote",
			request: CreateQuoteRequest{
				ProjectID: 1,
				ProductID: 1,
				QuoteType: constants.QuoteTypes.Vendor,
			},
			userID: 1,
			setupMocks: func(mq *MockQueries) {
				mq.checkQuoteExistsResult = 1 // Quote already exists
			},
			expectedError: ErrDuplicateQuote,
			validateResult: func(result *Info) bool {
				return result == nil
			},
		},
		{
			name: "database error on create",
			request: CreateQuoteRequest{
				ProjectID: 1,
				ProductID: 1,
				QuoteType: constants.QuoteTypes.Vendor,
			},
			userID: 1,
			setupMocks: func(mq *MockQueries) {
				mq.createQuoteError = errors.New("database error")
			},
			expectedError: errors.New("create quote: database error"),
			validateResult: func(result *Info) bool {
				return result == nil
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			service, mockQueries := createTestService()
			tt.setupMocks(mockQueries)

			// Execute
			result, err := service.CreateQuote(context.Background(), tt.request, tt.userID)

			// Assert
			if tt.expectedError != nil {
				if err == nil || (err.Error() != tt.expectedError.Error() && !errors.Is(err, tt.expectedError)) {
					t.Errorf("expected error %v, got %v", tt.expectedError, err)
				}
			} else if err != nil {
				t.Errorf("expected no error, got %v", err)
			}

			if !tt.validateResult(result) {
				t.Errorf("result validation failed")
			}
		})
	}
}

func TestService_GetQuoteByID(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		quoteID        int32
		setupMocks     func(*MockQueries)
		expectedError  error
		validateResult func(*Info) bool
	}{
		{
			name:    "successful get quote",
			quoteID: 1,
			setupMocks: func(mq *MockQueries) {
				// Default mocks are sufficient
			},
			expectedError: nil,
			validateResult: func(result *Info) bool {
				return result != nil && result.ID == 1
			},
		},
		{
			name:    "quote not found",
			quoteID: 999,
			setupMocks: func(mq *MockQueries) {
				mq.getQuoteByIDError = sql.ErrNoRows
			},
			expectedError: ErrQuoteNotFound,
			validateResult: func(result *Info) bool {
				return result == nil
			},
		},
		{
			name:    "database error",
			quoteID: 1,
			setupMocks: func(mq *MockQueries) {
				mq.getQuoteByIDError = errors.New("database error")
			},
			expectedError: errors.New("get quote: database error"),
			validateResult: func(result *Info) bool {
				return result == nil
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			service, mockQueries := createTestService()
			tt.setupMocks(mockQueries)

			// Execute
			result, err := service.GetQuoteByID(context.Background(), tt.quoteID)

			// Assert
			if tt.expectedError != nil {
				if err == nil || (err.Error() != tt.expectedError.Error() && !errors.Is(err, tt.expectedError)) {
					t.Errorf("expected error %v, got %v", tt.expectedError, err)
				}
			} else if err != nil {
				t.Errorf("expected no error, got %v", err)
			}

			if !tt.validateResult(result) {
				t.Errorf("result validation failed")
			}
		})
	}
}

func TestService_GetQuoteWithDetails(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		quoteID        int32
		setupMocks     func(*MockQueries)
		expectedError  error
		validateResult func(*Details) bool
	}{
		{
			name:    "successful get quote with details",
			quoteID: 1,
			setupMocks: func(mq *MockQueries) {
				mq.getQuoteWithDetailsRow = sqlc.GetQuoteByIDWithDetailsRow{
					ID:             1,
					ProjectID:      1,
					ProductID:      1,
					UserID:         1,
					QuoteType:      constants.QuoteTypes.Vendor,
					Status:         constants.QuoteStatuses.Pending,
					SameAsBidPrice: pgtype.Bool{Bool: false, Valid: true},
					CreatedAt:      pgtype.Timestamp{Time: time.Now(), Valid: true},
					UpdatedAt:      pgtype.Timestamp{Time: time.Now(), Valid: true},
					ProjectName:    pgtype.Text{String: "Test Project", Valid: true},
					ProductName:    pgtype.Text{String: "Test Product", Valid: true},
					ProductPid:     pgtype.Int4{Int32: 1001, Valid: true},
					UserUsername:   pgtype.Text{String: "testuser", Valid: true},
					UserEmail:      pgtype.Text{String: "<EMAIL>", Valid: true},
				}
			},
			expectedError: nil,
			validateResult: func(result *Details) bool {
				return result != nil &&
					result.ID == 1 &&
					result.ProjectName == "Test Project" &&
					result.ProductName == "Test Product" &&
					result.UserUsername == "testuser" &&
					*result.UserEmail == "<EMAIL>"
			},
		},
		{
			name:    "quote not found",
			quoteID: 999,
			setupMocks: func(mq *MockQueries) {
				mq.getQuoteWithDetailsError = sql.ErrNoRows
			},
			expectedError: ErrQuoteNotFound,
			validateResult: func(result *Details) bool {
				return result == nil
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			service, mockQueries := createTestService()
			tt.setupMocks(mockQueries)

			// Execute
			result, err := service.GetQuoteWithDetails(context.Background(), tt.quoteID)

			// Assert
			if tt.expectedError != nil {
				if err == nil || (err.Error() != tt.expectedError.Error() && !errors.Is(err, tt.expectedError)) {
					t.Errorf("expected error %v, got %v", tt.expectedError, err)
				}
			} else if err != nil {
				t.Errorf("expected no error, got %v", err)
			}

			if !tt.validateResult(result) {
				t.Errorf("result validation failed")
			}
		})
	}
}

func TestService_UpdateQuote(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		quoteID        int32
		request        UpdateQuoteRequest
		userID         int32
		setupMocks     func(*MockQueries)
		expectedError  error
		validateResult func(*Info) bool
	}{
		{
			name:    "successful update",
			quoteID: 1,
			request: UpdateQuoteRequest{
				MarketPrice:    floatPtr(1100.0),
				InternetPrice:  floatPtr(1000.0),
				SameAsBidPrice: false,
				Remark:         "Updated quote",
			},
			userID: 1,
			setupMocks: func(mq *MockQueries) {
				mq.updateQuoteResult = sqlc.Quote{
					ID:             1,
					ProjectID:      1,
					ProductID:      1,
					UserID:         1,
					QuoteType:      constants.QuoteTypes.Vendor,
					Status:         constants.QuoteStatuses.Resent,
					MarketPrice:    pgtype.Numeric{Valid: true},
					InternetPrice:  pgtype.Numeric{Valid: true},
					SameAsBidPrice: pgtype.Bool{Bool: false, Valid: true},
					CreatedAt:      pgtype.Timestamp{Time: time.Now(), Valid: true},
					UpdatedAt:      pgtype.Timestamp{Time: time.Now(), Valid: true},
				}
			},
			expectedError: nil,
			validateResult: func(result *Info) bool {
				return result != nil && result.Status == constants.QuoteStatuses.Resent
			},
		},
		{
			name:    "quote not found",
			quoteID: 999,
			request: UpdateQuoteRequest{},
			userID:  1,
			setupMocks: func(mq *MockQueries) {
				mq.getQuoteByIDError = sql.ErrNoRows
			},
			expectedError: ErrQuoteNotFound,
			validateResult: func(result *Info) bool {
				return result == nil
			},
		},
		{
			name:    "unauthorized user",
			quoteID: 1,
			request: UpdateQuoteRequest{},
			userID:  999, // Different user
			setupMocks: func(mq *MockQueries) {
				// Default quote has userID = 1
			},
			expectedError: ErrUnauthorized,
			validateResult: func(result *Info) bool {
				return result == nil
			},
		},
		{
			name:    "cannot update approved quote",
			quoteID: 1,
			request: UpdateQuoteRequest{},
			userID:  1,
			setupMocks: func(mq *MockQueries) {
				mq.getQuoteByIDResult.Status = constants.QuoteStatuses.Approved
			},
			expectedError: errors.New("只有待審或退件狀態的報價可以修改"),
			validateResult: func(result *Info) bool {
				return result == nil
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			service, mockQueries := createTestService()
			tt.setupMocks(mockQueries)

			// Execute
			result, err := service.UpdateQuote(context.Background(), tt.quoteID, tt.request, tt.userID)

			// Assert
			if tt.expectedError != nil {
				if err == nil || (err.Error() != tt.expectedError.Error() && !errors.Is(err, tt.expectedError)) {
					t.Errorf("expected error %v, got %v", tt.expectedError, err)
				}
			} else if err != nil {
				t.Errorf("expected no error, got %v", err)
			}

			if !tt.validateResult(result) {
				t.Errorf("result validation failed")
			}
		})
	}
}

func TestService_DeleteQuote(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name          string
		quoteID       int32
		userID        int32
		setupMocks    func(*MockQueries)
		expectedError error
	}{
		{
			name:    "successful delete",
			quoteID: 1,
			userID:  1,
			setupMocks: func(mq *MockQueries) {
				// Default mocks are sufficient
			},
			expectedError: nil,
		},
		{
			name:    "quote not found",
			quoteID: 999,
			userID:  1,
			setupMocks: func(mq *MockQueries) {
				mq.getQuoteByIDError = sql.ErrNoRows
			},
			expectedError: ErrQuoteNotFound,
		},
		{
			name:    "unauthorized user",
			quoteID: 1,
			userID:  999,
			setupMocks: func(mq *MockQueries) {
				// Default quote has userID = 1
			},
			expectedError: ErrUnauthorized,
		},
		{
			name:    "delete error",
			quoteID: 1,
			userID:  1,
			setupMocks: func(mq *MockQueries) {
				mq.deleteQuoteError = errors.New("database error")
			},
			expectedError: errors.New("delete quote: database error"),
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			service, mockQueries := createTestService()
			tt.setupMocks(mockQueries)

			// Execute
			err := service.DeleteQuote(context.Background(), tt.quoteID, tt.userID)

			// Assert
			if tt.expectedError != nil {
				if err == nil || (err.Error() != tt.expectedError.Error() && !errors.Is(err, tt.expectedError)) {
					t.Errorf("expected error %v, got %v", tt.expectedError, err)
				}
			} else if err != nil {
				t.Errorf("expected no error, got %v", err)
			}
		})
	}
}

func TestService_ReviewQuote(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		quoteID        int32
		request        ReviewQuoteRequest
		reviewerID     int32
		setupMocks     func(*MockQueries)
		expectedError  error
		validateResult func(*Info) bool
	}{
		{
			name:    "successful approval",
			quoteID: 1,
			request: ReviewQuoteRequest{
				Status:      constants.QuoteStatuses.Approved,
				AdminRemark: "Approved",
			},
			reviewerID: 2,
			setupMocks: func(mq *MockQueries) {
				mq.updateQuoteStatusResult = sqlc.Quote{
					ID:          1,
					Status:      constants.QuoteStatuses.Approved,
					ReviewedBy:  pgtype.Int4{Int32: 2, Valid: true},
					ReviewedAt:  pgtype.Timestamp{Time: time.Now(), Valid: true},
					AdminRemark: pgtype.Text{String: "Approved", Valid: true},
				}
			},
			expectedError: nil,
			validateResult: func(result *Info) bool {
				return result != nil && result.Status == constants.QuoteStatuses.Approved
			},
		},
		{
			name:    "successful rejection",
			quoteID: 1,
			request: ReviewQuoteRequest{
				Status:      constants.QuoteStatuses.Rejected,
				AdminRemark: "Price too high",
			},
			reviewerID: 2,
			setupMocks: func(mq *MockQueries) {
				mq.updateQuoteStatusResult = sqlc.Quote{
					ID:          1,
					Status:      constants.QuoteStatuses.Rejected,
					ReviewedBy:  pgtype.Int4{Int32: 2, Valid: true},
					AdminRemark: pgtype.Text{String: "Price too high", Valid: true},
				}
			},
			expectedError: nil,
			validateResult: func(result *Info) bool {
				return result != nil && result.Status == constants.QuoteStatuses.Rejected
			},
		},
		{
			name:    "quote not found",
			quoteID: 999,
			request: ReviewQuoteRequest{
				Status: constants.QuoteStatuses.Approved,
			},
			reviewerID: 2,
			setupMocks: func(mq *MockQueries) {
				mq.getQuoteByIDError = sql.ErrNoRows
			},
			expectedError: ErrQuoteNotFound,
			validateResult: func(result *Info) bool {
				return result == nil
			},
		},
		{
			name:    "already reviewed",
			quoteID: 1,
			request: ReviewQuoteRequest{
				Status: constants.QuoteStatuses.Approved,
			},
			reviewerID: 2,
			setupMocks: func(mq *MockQueries) {
				mq.getQuoteByIDResult.Status = constants.QuoteStatuses.Approved
				mq.getQuoteByIDResult.ReviewedBy = pgtype.Int4{Int32: 3, Valid: true}
			},
			expectedError: ErrAlreadyReviewed,
			validateResult: func(result *Info) bool {
				return result == nil
			},
		},
		{
			name:    "invalid status",
			quoteID: 1,
			request: ReviewQuoteRequest{
				Status: constants.QuoteStatuses.Pending, // Invalid for review
			},
			reviewerID: 2,
			setupMocks: func(mq *MockQueries) {
				// Default mocks
			},
			expectedError: ErrInvalidStatus,
			validateResult: func(result *Info) bool {
				return result == nil
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			service, mockQueries := createTestService()
			tt.setupMocks(mockQueries)

			// Execute
			result, err := service.ReviewQuote(context.Background(), tt.quoteID, tt.request, tt.reviewerID)

			// Assert
			if tt.expectedError != nil {
				if err == nil || (err.Error() != tt.expectedError.Error() && !errors.Is(err, tt.expectedError)) {
					t.Errorf("expected error %v, got %v", tt.expectedError, err)
				}
			} else if err != nil {
				t.Errorf("expected no error, got %v", err)
			}

			if !tt.validateResult(result) {
				t.Errorf("result validation failed")
			}
		})
	}
}

func TestService_ListQuotes(t *testing.T) {
	t.Parallel()

	projectID := int32(1)
	userID := int32(1)
	quoteType := constants.QuoteTypes.Vendor
	status := constants.QuoteStatuses.Pending

	tests := []struct {
		name           string
		request        ListQuotesRequest
		setupMocks     func(*MockQueries)
		expectedError  error
		validateResult func(*ListQuotesResponse) bool
	}{
		{
			name: "successful list quotes",
			request: ListQuotesRequest{
				ProjectID: &projectID,
				UserID:    &userID,
				QuoteType: &quoteType,
				Status:    &status,
				Pagination: types.PaginationParams{
					Page:     1,
					PageSize: 20,
				},
			},
			setupMocks: func(mq *MockQueries) {
				mq.countQuotesResult = 2
				mq.listQuotesResult = []sqlc.ListQuotesRow{
					{
						ID:             1,
						ProjectID:      1,
						ProductID:      1,
						UserID:         1,
						QuoteType:      constants.QuoteTypes.Vendor,
						Status:         constants.QuoteStatuses.Pending,
						SameAsBidPrice: pgtype.Bool{Bool: false, Valid: true},
						CreatedAt:      pgtype.Timestamp{Time: time.Now(), Valid: true},
						UpdatedAt:      pgtype.Timestamp{Time: time.Now(), Valid: true},
						ProjectName:    pgtype.Text{String: "Test Project", Valid: true},
						ProductName:    pgtype.Text{String: "Test Product", Valid: true},
						ProductPid:     pgtype.Int4{Int32: 1001, Valid: true},
						UserUsername:   pgtype.Text{String: "testuser", Valid: true},
					},
					{
						ID:             2,
						ProjectID:      1,
						ProductID:      2,
						UserID:         1,
						QuoteType:      constants.QuoteTypes.Vendor,
						Status:         constants.QuoteStatuses.Pending,
						SameAsBidPrice: pgtype.Bool{Bool: false, Valid: true},
						CreatedAt:      pgtype.Timestamp{Time: time.Now(), Valid: true},
						UpdatedAt:      pgtype.Timestamp{Time: time.Now(), Valid: true},
						ProjectName:    pgtype.Text{String: "Test Project", Valid: true},
						ProductName:    pgtype.Text{String: "Test Product 2", Valid: true},
						ProductPid:     pgtype.Int4{Int32: 1002, Valid: true},
						UserUsername:   pgtype.Text{String: "testuser", Valid: true},
					},
				}
			},
			expectedError: nil,
			validateResult: func(result *ListQuotesResponse) bool {
				return result != nil && len(result.Data) == 2 && result.Pagination.Total == 2
			},
		},
		{
			name: "empty results",
			request: ListQuotesRequest{
				Pagination: types.PaginationParams{
					Page:     1,
					PageSize: 20,
				},
			},
			setupMocks: func(mq *MockQueries) {
				mq.countQuotesResult = 0
				mq.listQuotesResult = []sqlc.ListQuotesRow{}
			},
			expectedError: nil,
			validateResult: func(result *ListQuotesResponse) bool {
				return result != nil && len(result.Data) == 0 && result.Pagination.Total == 0
			},
		},
		{
			name: "database error on count",
			request: ListQuotesRequest{
				Pagination: types.PaginationParams{
					Page:     1,
					PageSize: 20,
				},
			},
			setupMocks: func(mq *MockQueries) {
				mq.countQuotesError = errors.New("database error")
			},
			expectedError: errors.New("count quotes: database error"),
			validateResult: func(result *ListQuotesResponse) bool {
				return result == nil
			},
		},
		{
			name: "database error on list",
			request: ListQuotesRequest{
				Pagination: types.PaginationParams{
					Page:     1,
					PageSize: 20,
				},
			},
			setupMocks: func(mq *MockQueries) {
				mq.countQuotesResult = 10
				mq.listQuotesError = errors.New("database error")
			},
			expectedError: errors.New("list quotes: database error"),
			validateResult: func(result *ListQuotesResponse) bool {
				return result == nil
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			service, mockQueries := createTestService()
			tt.setupMocks(mockQueries)

			// Execute
			result, err := service.ListQuotes(context.Background(), tt.request)

			// Assert
			if tt.expectedError != nil {
				if err == nil || (err.Error() != tt.expectedError.Error() && !errors.Is(err, tt.expectedError)) {
					t.Errorf("expected error %v, got %v", tt.expectedError, err)
				}
			} else if err != nil {
				t.Errorf("expected no error, got %v", err)
			}

			if !tt.validateResult(result) {
				t.Errorf("result validation failed")
			}
		})
	}
}

func TestService_GetQuotesByProject(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		projectID      int32
		setupMocks     func(*MockQueries)
		expectedError  error
		validateResult func([]Details) bool
	}{
		{
			name:      "successful get project quotes",
			projectID: 1,
			setupMocks: func(mq *MockQueries) {
				mq.getQuotesByProjectRows = []sqlc.GetQuotesByProjectRow{
					{
						ID:             1,
						ProjectID:      1,
						ProductID:      1,
						UserID:         1,
						QuoteType:      constants.QuoteTypes.Vendor,
						Status:         constants.QuoteStatuses.Pending,
						SameAsBidPrice: pgtype.Bool{Bool: false, Valid: true},
						CreatedAt:      pgtype.Timestamp{Time: time.Now(), Valid: true},
						UpdatedAt:      pgtype.Timestamp{Time: time.Now(), Valid: true},
						ProductName:    pgtype.Text{String: "Test Product", Valid: true},
						ProductPid:     pgtype.Int4{Int32: 1001, Valid: true},
						UserUsername:   pgtype.Text{String: "testuser", Valid: true},
					},
				}
			},
			expectedError: nil,
			validateResult: func(result []Details) bool {
				return len(result) == 1 && result[0].ProductName == "Test Product"
			},
		},
		{
			name:      "database error",
			projectID: 1,
			setupMocks: func(mq *MockQueries) {
				mq.getQuotesByProjectError = errors.New("database error")
			},
			expectedError: errors.New("get project quotes: database error"),
			validateResult: func(result []Details) bool {
				return result == nil
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			service, mockQueries := createTestService()
			tt.setupMocks(mockQueries)

			// Execute
			result, err := service.GetQuotesByProject(context.Background(), tt.projectID)

			// Assert
			if tt.expectedError != nil {
				if err == nil || err.Error() != tt.expectedError.Error() {
					t.Errorf("expected error %v, got %v", tt.expectedError, err)
				}
			} else if err != nil {
				t.Errorf("expected no error, got %v", err)
			}

			if !tt.validateResult(result) {
				t.Errorf("result validation failed")
			}
		})
	}
}

func TestService_GetQuotesByUser(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		userID         int32
		pagination     types.PaginationParams
		setupMocks     func(*MockQueries)
		expectedError  error
		validateResult func(*ListQuotesResponse) bool
	}{
		{
			name:   "successful get user quotes",
			userID: 1,
			pagination: types.PaginationParams{
				Page:     1,
				PageSize: 10,
			},
			setupMocks: func(mq *MockQueries) {
				mq.getQuotesByUserRows = []sqlc.GetQuotesByUserRow{
					{
						ID:             1,
						ProjectID:      1,
						ProductID:      1,
						UserID:         1,
						QuoteType:      constants.QuoteTypes.Vendor,
						Status:         constants.QuoteStatuses.Pending,
						SameAsBidPrice: pgtype.Bool{Bool: false, Valid: true},
						CreatedAt:      pgtype.Timestamp{Time: time.Now(), Valid: true},
						UpdatedAt:      pgtype.Timestamp{Time: time.Now(), Valid: true},
						ProjectName:    pgtype.Text{String: "Test Project", Valid: true},
						ProductName:    pgtype.Text{String: "Test Product", Valid: true},
						ProductPid:     pgtype.Int4{Int32: 1001, Valid: true},
					},
				}
			},
			expectedError: nil,
			validateResult: func(result *ListQuotesResponse) bool {
				return result != nil && len(result.Data) == 1
			},
		},
		{
			name:   "database error",
			userID: 1,
			pagination: types.PaginationParams{
				Page:     1,
				PageSize: 10,
			},
			setupMocks: func(mq *MockQueries) {
				mq.getQuotesByUserError = errors.New("database error")
			},
			expectedError: errors.New("get user quotes: database error"),
			validateResult: func(result *ListQuotesResponse) bool {
				return result == nil
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			service, mockQueries := createTestService()
			tt.setupMocks(mockQueries)

			// Execute
			result, err := service.GetQuotesByUser(context.Background(), tt.userID, tt.pagination)

			// Assert
			if tt.expectedError != nil {
				if err == nil || err.Error() != tt.expectedError.Error() {
					t.Errorf("expected error %v, got %v", tt.expectedError, err)
				}
			} else if err != nil {
				t.Errorf("expected no error, got %v", err)
			}

			if !tt.validateResult(result) {
				t.Errorf("result validation failed")
			}
		})
	}
}

func TestService_GetPendingQuotes(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		setupMocks     func(*MockQueries)
		expectedError  error
		validateResult func([]Details) bool
	}{
		{
			name: "successful get pending quotes",
			setupMocks: func(mq *MockQueries) {
				mq.getPendingQuotesRows = []sqlc.GetPendingQuotesRow{
					{
						ID:             1,
						ProjectID:      1,
						ProductID:      1,
						UserID:         1,
						QuoteType:      constants.QuoteTypes.Vendor,
						Status:         constants.QuoteStatuses.Pending,
						SameAsBidPrice: pgtype.Bool{Bool: false, Valid: true},
						CreatedAt:      pgtype.Timestamp{Time: time.Now(), Valid: true},
						UpdatedAt:      pgtype.Timestamp{Time: time.Now(), Valid: true},
						ProjectName:    pgtype.Text{String: "Test Project", Valid: true},
						ProductName:    pgtype.Text{String: "Test Product", Valid: true},
						ProductPid:     pgtype.Int4{Int32: 1001, Valid: true},
						UserUsername:   pgtype.Text{String: "testuser", Valid: true},
					},
				}
			},
			expectedError: nil,
			validateResult: func(result []Details) bool {
				return len(result) == 1 && result[0].Status == constants.QuoteStatuses.Pending
			},
		},
		{
			name: "database error",
			setupMocks: func(mq *MockQueries) {
				mq.getPendingQuotesError = errors.New("database error")
			},
			expectedError: errors.New("get pending quotes: database error"),
			validateResult: func(result []Details) bool {
				return result == nil
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			service, mockQueries := createTestService()
			tt.setupMocks(mockQueries)

			// Execute
			result, err := service.GetPendingQuotes(context.Background())

			// Assert
			if tt.expectedError != nil {
				if err == nil || err.Error() != tt.expectedError.Error() {
					t.Errorf("expected error %v, got %v", tt.expectedError, err)
				}
			} else if err != nil {
				t.Errorf("expected no error, got %v", err)
			}

			if !tt.validateResult(result) {
				t.Errorf("result validation failed")
			}
		})
	}
}

func TestService_BatchReviewQuotes(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name          string
		quoteIDs      []int32
		status        sqlc.QuoteStatus
		adminRemark   string
		reviewerID    int32
		setupMocks    func(*MockQueries)
		expectedError error
	}{
		{
			name:        "successful batch approval",
			quoteIDs:    []int32{1, 2, 3},
			status:      constants.QuoteStatuses.Approved,
			adminRemark: "Batch approved",
			reviewerID:  2,
			setupMocks: func(mq *MockQueries) {
				// Default mocks are sufficient
			},
			expectedError: nil,
		},
		{
			name:        "successful batch rejection",
			quoteIDs:    []int32{1, 2, 3},
			status:      constants.QuoteStatuses.Rejected,
			adminRemark: "Batch rejected",
			reviewerID:  2,
			setupMocks: func(mq *MockQueries) {
				// Default mocks are sufficient
			},
			expectedError: nil,
		},
		{
			name:          "invalid status",
			quoteIDs:      []int32{1, 2, 3},
			status:        constants.QuoteStatuses.Pending,
			adminRemark:   "",
			reviewerID:    2,
			setupMocks:    func(mq *MockQueries) {},
			expectedError: ErrInvalidStatus,
		},
		{
			name:        "database error",
			quoteIDs:    []int32{1, 2, 3},
			status:      constants.QuoteStatuses.Approved,
			adminRemark: "Batch approved",
			reviewerID:  2,
			setupMocks: func(mq *MockQueries) {
				mq.batchUpdateError = errors.New("database error")
			},
			expectedError: errors.New("batch update quotes: database error"),
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			service, mockQueries := createTestService()
			tt.setupMocks(mockQueries)

			// Execute
			err := service.BatchReviewQuotes(context.Background(), tt.quoteIDs, tt.status, tt.adminRemark, tt.reviewerID)

			// Assert
			if tt.expectedError != nil {
				if err == nil || (err.Error() != tt.expectedError.Error() && !errors.Is(err, tt.expectedError)) {
					t.Errorf("expected error %v, got %v", tt.expectedError, err)
				}
			} else if err != nil {
				t.Errorf("expected no error, got %v", err)
			}
		})
	}
}

// Edge case tests
func TestService_EdgeCases(t *testing.T) {
	t.Parallel()

	t.Run("nil price handling", func(t *testing.T) {
		service, _ := createTestService()

		req := CreateQuoteRequest{
			ProjectID: 1,
			ProductID: 1,
			QuoteType: constants.QuoteTypes.Vendor,
			// All prices are nil
		}

		quote, err := service.CreateQuote(context.Background(), req, 1)
		if err != nil {
			t.Errorf("expected no error for nil prices, got %v", err)
		}

		if quote == nil {
			t.Error("expected quote to be created with nil prices")
		}
	})

	t.Run("empty remark handling", func(t *testing.T) {
		service, _ := createTestService()

		req := CreateQuoteRequest{
			ProjectID: 1,
			ProductID: 1,
			QuoteType: constants.QuoteTypes.Vendor,
			Remark:    "", // Empty remark
		}

		quote, err := service.CreateQuote(context.Background(), req, 1)
		if err != nil {
			t.Errorf("expected no error for empty remark, got %v", err)
		}

		if quote == nil || quote.Remark != "" {
			t.Error("expected quote to be created with empty remark")
		}
	})

	t.Run("quote conversion handles null values", func(t *testing.T) {
		service, mockQueries := createTestService()

		// Test with quote that has null values
		mockQueries.getQuoteByIDResult = sqlc.Quote{
			ID:             1,
			ProjectID:      1,
			ProductID:      1,
			UserID:         1,
			QuoteType:      constants.QuoteTypes.Vendor,
			Status:         constants.QuoteStatuses.Pending,
			MarketPrice:    pgtype.Numeric{Valid: false}, // null
			InternetPrice:  pgtype.Numeric{Valid: false}, // null
			SameAsBidPrice: pgtype.Bool{Bool: false, Valid: true},
			Remark:         pgtype.Text{Valid: false},      // null
			AdminRemark:    pgtype.Text{Valid: false},      // null
			ReviewedAt:     pgtype.Timestamp{Valid: false}, // null
			ReviewedBy:     pgtype.Int4{Valid: false},      // null
			BatchID:        pgtype.Int4{Valid: false},      // null
			CreatedAt:      pgtype.Timestamp{Time: time.Now(), Valid: true},
			UpdatedAt:      pgtype.Timestamp{Time: time.Now(), Valid: true},
		}

		quote, err := service.GetQuoteByID(context.Background(), 1)
		if err != nil {
			t.Fatalf("expected no error, got %v", err)
		}

		// Verify nulls are handled properly
		if quote.MarketPrice != nil {
			t.Error("expected MarketPrice to be nil")
		}
		if quote.InternetPrice != nil {
			t.Error("expected InternetPrice to be nil")
		}
		if quote.Remark != "" {
			t.Error("expected empty Remark")
		}
		if quote.AdminRemark != "" {
			t.Error("expected empty AdminRemark")
		}
		if quote.ReviewedAt != nil {
			t.Error("expected ReviewedAt to be nil")
		}
		if quote.ReviewedBy != nil {
			t.Error("expected ReviewedBy to be nil")
		}
		if quote.BatchID != nil {
			t.Error("expected BatchID to be nil")
		}
	})
}

// Helper functions removed - using the one from business_logic_test.go

// Test the helper conversion functions
func TestService_ConversionFunctions(t *testing.T) {
	t.Parallel()

	service := &Service{}

	t.Run("convertProjectQuoteToQuoteWithDetails", func(t *testing.T) {
		row := sqlc.GetQuotesByProjectRow{
			ID:             1,
			ProjectID:      1,
			ProductID:      1,
			UserID:         1,
			QuoteType:      constants.QuoteTypes.Vendor,
			Status:         constants.QuoteStatuses.Pending,
			SameAsBidPrice: pgtype.Bool{Bool: false, Valid: true},
			CreatedAt:      pgtype.Timestamp{Time: time.Now(), Valid: true},
			UpdatedAt:      pgtype.Timestamp{Time: time.Now(), Valid: true},
			ProductName:    pgtype.Text{String: "Test Product", Valid: true},
			ProductPid:     pgtype.Int4{Int32: 1001, Valid: true},
			UserUsername:   pgtype.Text{String: "testuser", Valid: true},
		}

		result := service.convertProjectQuoteToQuoteWithDetails(row)

		if result.ID != 1 {
			t.Errorf("expected ID 1, got %d", result.ID)
		}
		if result.ProductName != "Test Product" {
			t.Errorf("expected ProductName 'Test Product', got %s", result.ProductName)
		}
		if result.ProductPID != "1001" {
			t.Errorf("expected ProductPID '1001', got %s", result.ProductPID)
		}
		if result.UserUsername != "testuser" {
			t.Errorf("expected UserUsername 'testuser', got %s", result.UserUsername)
		}
	})

	t.Run("quoteTypeToNullable", func(t *testing.T) {
		// Test with non-nil value
		qt := constants.QuoteTypes.Vendor
		result := service.quoteTypeToNullable(&qt)
		if !result.Valid || result.QuoteType != qt {
			t.Error("expected valid QuoteType")
		}

		// Test with nil value
		result = service.quoteTypeToNullable(nil)
		if result.Valid {
			t.Error("expected invalid QuoteType for nil input")
		}
	})

	t.Run("quoteStatusToNullable", func(t *testing.T) {
		// Test with non-nil value
		qs := constants.QuoteStatuses.Pending
		result := service.quoteStatusToNullable(&qs)
		if !result.Valid || result.QuoteStatus != qs {
			t.Error("expected valid QuoteStatus")
		}

		// Test with nil value
		result = service.quoteStatusToNullable(nil)
		if result.Valid {
			t.Error("expected invalid QuoteStatus for nil input")
		}
	})
}

// Test proper handling of GetQuotesByUser pagination
func TestService_GetQuotesByUser_Pagination(t *testing.T) {
	t.Parallel()

	service, mockQueries := createTestService()

	// Setup mock data for pagination test
	mockQueries.getQuotesByUserRows = []sqlc.GetQuotesByUserRow{
		{
			ID:             1,
			ProjectID:      1,
			ProductID:      1,
			UserID:         1,
			QuoteType:      constants.QuoteTypes.Vendor,
			Status:         constants.QuoteStatuses.Pending,
			SameAsBidPrice: pgtype.Bool{Bool: false, Valid: true},
			CreatedAt:      pgtype.Timestamp{Time: time.Now(), Valid: true},
			UpdatedAt:      pgtype.Timestamp{Time: time.Now(), Valid: true},
			ProjectName:    pgtype.Text{String: "Project 1", Valid: true},
			ProductName:    pgtype.Text{String: "Product 1", Valid: true},
			ProductPid:     pgtype.Int4{Int32: 1001, Valid: true},
		},
	}

	pagination := types.PaginationParams{
		Page:     1,
		PageSize: 10,
	}

	result, err := service.GetQuotesByUser(context.Background(), 1, pagination)
	if err != nil {
		t.Fatalf("expected no error, got %v", err)
	}

	// Verify pagination meta
	if result.Pagination.Page != 1 {
		t.Errorf("expected page 1, got %d", result.Pagination.Page)
	}
	if result.Pagination.PageSize != 10 {
		t.Errorf("expected page size 10, got %d", result.Pagination.PageSize)
	}
	if len(result.Data) != 1 {
		t.Errorf("expected 1 result, got %d", len(result.Data))
	}
}

// Test proper error wrapping
func TestService_ErrorWrapping(t *testing.T) {
	t.Parallel()

	service, mockQueries := createTestService()
	ctx := context.Background()

	// Test database error wrapping in CreateQuote
	mockQueries.getProjectByIDError = errors.New("connection refused")
	_, err := service.CreateQuote(ctx, CreateQuoteRequest{ProjectID: 1, ProductID: 1, QuoteType: constants.QuoteTypes.Vendor}, 1)
	if err == nil || !contains(err.Error(), "get project") {
		t.Errorf("expected wrapped error with 'get project', got %v", err)
	}

	// Test database error wrapping in GetQuoteByID
	mockQueries.getQuoteByIDError = errors.New("connection timeout")
	_, err = service.GetQuoteByID(ctx, 1)
	if err == nil || !contains(err.Error(), "get quote") {
		t.Errorf("expected wrapped error with 'get quote', got %v", err)
	}

	// Test database error wrapping in UpdateQuote
	mockQueries.getQuoteByIDError = nil
	mockQueries.updateQuoteError = errors.New("constraint violation")
	_, err = service.UpdateQuote(ctx, 1, UpdateQuoteRequest{}, 1)
	if err == nil || !contains(err.Error(), "update quote") {
		t.Errorf("expected wrapped error with 'update quote', got %v", err)
	}
}

// Test GetQuotesByUser conversion function
func TestService_GetQuotesByUser_Conversion(t *testing.T) {
	t.Parallel()

	service := &Service{}

	// Create a GetQuotesByUserRow to test conversion
	row := sqlc.GetQuotesByUserRow{
		ID:             1,
		ProjectID:      1,
		ProductID:      1,
		UserID:         1,
		QuoteType:      constants.QuoteTypes.Vendor,
		Status:         constants.QuoteStatuses.Pending,
		SameAsBidPrice: pgtype.Bool{Bool: false, Valid: true},
		CreatedAt:      pgtype.Timestamp{Time: time.Now(), Valid: true},
		UpdatedAt:      pgtype.Timestamp{Time: time.Now(), Valid: true},
		ProjectName:    pgtype.Text{String: "Test Project", Valid: true},
		ProductName:    pgtype.Text{String: "Test Product", Valid: true},
		ProductPid:     pgtype.Int4{Int32: 1001, Valid: true},
	}

	// Convert using the internal method
	result := service.convertUserQuoteToQuoteWithDetails(row)

	if result.ID != 1 {
		t.Errorf("expected ID 1, got %d", result.ID)
	}
	if result.ProjectName != "Test Project" {
		t.Errorf("expected ProjectName 'Test Project', got %s", result.ProjectName)
	}
	if result.ProductName != "Test Product" {
		t.Errorf("expected ProductName 'Test Product', got %s", result.ProductName)
	}
	if result.ProductPID != "1001" {
		t.Errorf("expected ProductPID '1001', got %s", result.ProductPID)
	}
	// Username should be empty when null (but we don't assign it in convertUserQuoteToQuoteWithDetails)
	if result.UserUsername != "" {
		t.Errorf("expected empty UserUsername, got %s", result.UserUsername)
	}
}

// Test GetPendingQuotes conversion function
func TestService_GetPendingQuotes_Conversion(t *testing.T) {
	t.Parallel()

	service := &Service{}

	// Create a GetPendingQuotesRow to test conversion
	row := sqlc.GetPendingQuotesRow{
		ID:             1,
		ProjectID:      1,
		ProductID:      1,
		UserID:         1,
		QuoteType:      constants.QuoteTypes.Vendor,
		Status:         constants.QuoteStatuses.Pending,
		SameAsBidPrice: pgtype.Bool{Bool: false, Valid: true},
		CreatedAt:      pgtype.Timestamp{Time: time.Now(), Valid: true},
		UpdatedAt:      pgtype.Timestamp{Time: time.Now(), Valid: true},
		ProjectName:    pgtype.Text{String: "Test Project", Valid: true},
		ProductName:    pgtype.Text{String: "Test Product", Valid: true},
		ProductPid:     pgtype.Int4{Int32: 1001, Valid: true},
		UserUsername:   pgtype.Text{String: "testuser", Valid: true},
		UserEmail:      pgtype.Text{String: "<EMAIL>", Valid: true},
	}

	// Convert using the internal method
	result := service.convertPendingQuoteToQuoteWithDetails(row)

	if result.ID != 1 {
		t.Errorf("expected ID 1, got %d", result.ID)
	}
	if result.ProjectName != "Test Project" {
		t.Errorf("expected ProjectName 'Test Project', got %s", result.ProjectName)
	}
	if result.ProductName != "Test Product" {
		t.Errorf("expected ProductName 'Test Product', got %s", result.ProductName)
	}
	if result.ProductPID != "1001" {
		t.Errorf("expected ProductPID '1001', got %s", result.ProductPID)
	}
	if result.UserUsername != "testuser" {
		t.Errorf("expected UserUsername 'testuser', got %s", result.UserUsername)
	}
	// UserEmail should be populated for pending quotes
	if result.UserEmail == nil || *result.UserEmail != "<EMAIL>" {
		t.Errorf("expected UserEmail '<EMAIL>', got %v", result.UserEmail)
	}
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(substr) == 0 ||
		(len(s) > len(substr) && (s[:len(substr)] == substr || s[len(s)-len(substr):] == substr ||
			func() bool {
				for i := 1; i <= len(s)-len(substr); i++ {
					if s[i:i+len(substr)] == substr {
						return true
					}
				}
				return false
			}())))
}

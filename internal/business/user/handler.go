// Package user 提供用戶管理相關的 HTTP 處理器
// 負責處理用戶相關的 REST API 端點，包含用戶資料 CRUD、密碼變更等功能
package user

import (
	"context"
	"net/http"
	"strconv"
	"strings"

	"github.com/koopa0/pms-api-v2/internal/api"
	"github.com/koopa0/pms-api-v2/internal/business/auth"
	"github.com/koopa0/pms-api-v2/internal/constants"
	"github.com/koopa0/pms-api-v2/internal/platform/logger"
	"github.com/koopa0/pms-api-v2/internal/platform/middleware"
	"github.com/koopa0/pms-api-v2/sqlc"
)

// Handler 用戶 HTTP 處理器
// 負責處理所有用戶相關的 HTTP 請求，包含身份驗證、授權檢查和輸入驗證
type Handler struct {
	userService *Service       // 用戶業務邏輯服務 - 使用具體類型，遵循 Go 最佳實踐
	logger      *logger.Logger // 結構化日誌記錄器
}

// NewHandler 創建新的用戶處理器實例
// 直接接受具體的 Service 類型，避免不必要的抽象
func NewHandler(userService *Service, logger *logger.Logger) *Handler {
	return &Handler{
		userService: userService,
		logger:      logger,
	}
}

// GetUserProfile 處理 GET /api/users/profile
// 獲取當前登入用戶的個人資料
func (h *Handler) GetUserProfile(w http.ResponseWriter, r *http.Request) {
	userID, ok := auth.GetUserIDFromContext(r.Context())
	if !ok {
		api.Unauthorized(r.Context(), w, constants.ErrMsgUnauthorized)
		return
	}

	user, err := h.userService.GetUserByID(r.Context(), userID)
	if err != nil {
		h.handleServiceErrorWithContext(r.Context(), w, err)
		return
	}

	api.Success(r.Context(), w, user)
}

// UpdateUserProfile 處理 PUT /api/users/profile
// 更新當前登入用戶的個人資料
func (h *Handler) UpdateUserProfile(w http.ResponseWriter, r *http.Request) {
	userID, _, ok := auth.RequireAuth(w, r)
	if !ok {
		return
	}

	var req UpdateUserRequest
	if !api.DecodeJSONRequest(w, r, &req) {
		return
	}

	if !h.validateUpdateUserRequestWithContext(r.Context(), w, &req) {
		return
	}

	user, err := h.userService.UpdateUser(r.Context(), userID, req)
	if err != nil {
		h.handleServiceErrorWithContext(r.Context(), w, err)
		return
	}

	api.SuccessWithContext(r.Context(), w, user)
}

// ChangePassword 處理 POST /api/users/change-password
// 變更當前登入用戶的密碼
func (h *Handler) ChangePassword(w http.ResponseWriter, r *http.Request) {
	userID, ok := auth.GetUserIDFromContext(r.Context())
	if !ok {
		api.UnauthorizedWithContext(r.Context(), w, constants.ErrMsgUnauthorized)
		return
	}

	var req ChangePasswordRequest
	if !api.DecodeJSONRequest(w, r, &req) {
		return
	}

	if !h.validateChangePasswordRequestWithContext(r.Context(), w, &req) {
		return
	}

	if err := h.userService.ChangePassword(r.Context(), userID, req); err != nil {
		h.handleServiceErrorWithContext(r.Context(), w, err)
		return
	}

	api.Success(r.Context(), w, map[string]string{"message": constants.MsgPasswordChanged})
}

// ListUsers handles GET /api/users (admin only)
func (h *Handler) ListUsers(w http.ResponseWriter, r *http.Request) {
	if _, ok := auth.RequireAdmin(w, r); !ok {
		return
	}

	pagination := api.ParseRequestPagination(r)
	req := ListUsersRequest{
		Role:       sqlc.UserRole(r.URL.Query().Get("role")),
		Status:     sqlc.UserStatus(r.URL.Query().Get("status")),
		Pagination: pagination,
	}

	users, err := h.userService.ListUsers(r.Context(), req)
	if err != nil {
		h.handleServiceErrorWithContext(r.Context(), w, err)
		return
	}

	api.Success(r.Context(), w, users)
}

// GetUserByID handles GET /api/users/{id} (admin only)
func (h *Handler) GetUserByID(w http.ResponseWriter, r *http.Request) {
	if _, ok := auth.RequireAdmin(w, r); !ok {
		return
	}

	userID, ok := api.ParseIDParam(w, r, "id")
	if !ok {
		return
	}

	user, err := h.userService.GetUserByID(r.Context(), userID)
	if err != nil {
		h.handleServiceErrorWithContext(r.Context(), w, err)
		return
	}

	api.Success(r.Context(), w, user)
}

// UpdateUserStatus handles PATCH /api/users/{id}/status (admin only)
func (h *Handler) UpdateUserStatus(w http.ResponseWriter, r *http.Request) {
	if _, ok := auth.RequireAdmin(w, r); !ok {
		return
	}

	userID, ok := api.ParseIDParam(w, r, "id")
	if !ok {
		return
	}

	var req struct {
		Status sqlc.UserStatus `json:"status"`
	}
	if !api.DecodeJSONRequest(w, r, &req) {
		return
	}

	if !h.validateUserStatusWithContext(r.Context(), w, req.Status) {
		return
	}

	user, err := h.userService.UpdateUserStatus(r.Context(), userID, req.Status)
	if err != nil {
		h.handleServiceErrorWithContext(r.Context(), w, err)
		return
	}

	api.Success(r.Context(), w, user)
}

// 驗證相關方法

// validateUpdateUserRequest 驗證用戶資料更新請求
// 確保輸入數據符合業務規則和技術約束
func (h *Handler) validateUpdateUserRequest(req *UpdateUserRequest) error {
	// Create validation rules
	validationRules := []middleware.ValidationRule{
		{Field: "username", Value: req.Username, Rules: []string{"required", "min:" + strconv.Itoa(constants.UsernameMinLength), "max:" + strconv.Itoa(constants.UsernameMaxLength)}, DisplayName: "用戶名稱"},
		{Field: "email", Value: req.Email, Rules: []string{"required", "email"}, DisplayName: "電子郵件"},
	}

	if req.BackupEmail != nil {
		validationRules = append(validationRules, middleware.ValidationRule{
			Field: "backup_email", Value: *req.BackupEmail, Rules: []string{"email"}, DisplayName: "備用電子郵件",
		})
	}

	if req.JobTitle != nil {
		validationRules = append(validationRules, middleware.ValidationRule{
			Field: "job_title", Value: *req.JobTitle, Rules: []string{"max:" + strconv.Itoa(constants.JobTitleMaxLength)}, DisplayName: "職稱",
		})
	}

	if req.Mobile != nil {
		validationRules = append(validationRules, middleware.ValidationRule{
			Field: "mobile", Value: *req.Mobile, Rules: []string{"max:" + strconv.Itoa(constants.MobileMaxLength)}, DisplayName: "手機號碼",
		})
	}

	// Validate without response writer for testing
	validator := middleware.NewValidator(context.Background())
	for _, rule := range validationRules {
		validator.ValidateField(rule.Field, rule.Value, rule.Rules, rule.DisplayName)
	}

	result := validator.Result()
	if !result.Valid {
		return api.ErrBadRequest
	}
	return nil
}

// validateUpdateUserRequestWithContext 驗證用戶資料更新請求並回應錯誤
func (h *Handler) validateUpdateUserRequestWithContext(ctx context.Context, w http.ResponseWriter, req *UpdateUserRequest) bool {
	validationRules := []middleware.ValidationRule{
		{Field: "username", Value: req.Username, Rules: []string{"required", "min:" + strconv.Itoa(constants.UsernameMinLength), "max:" + strconv.Itoa(constants.UsernameMaxLength)}, DisplayName: "用戶名稱"},
		{Field: "email", Value: req.Email, Rules: []string{"required", "email"}, DisplayName: "電子郵件"},
	}

	if req.BackupEmail != nil {
		validationRules = append(validationRules, middleware.ValidationRule{
			Field: "backup_email", Value: *req.BackupEmail, Rules: []string{"email"}, DisplayName: "備用電子郵件",
		})
	}

	if req.JobTitle != nil {
		validationRules = append(validationRules, middleware.ValidationRule{
			Field: "job_title", Value: *req.JobTitle, Rules: []string{"max:" + strconv.Itoa(constants.JobTitleMaxLength)}, DisplayName: "職稱",
		})
	}

	if req.Mobile != nil {
		validationRules = append(validationRules, middleware.ValidationRule{
			Field: "mobile", Value: *req.Mobile, Rules: []string{"max:" + strconv.Itoa(constants.MobileMaxLength)}, DisplayName: "手機號碼",
		})
	}

	return middleware.ValidateFieldsAndRespond(ctx, w, validationRules)
}

// validateChangePasswordRequest 驗證密碼變更請求
// 確保密碼符合安全性要求
func (h *Handler) validateChangePasswordRequest(req *ChangePasswordRequest) error {
	validationRules := []middleware.ValidationRule{
		{Field: "old_password", Value: req.OldPassword, Rules: []string{"required"}, DisplayName: "現在密碼"},
		{Field: "new_password", Value: req.NewPassword, Rules: []string{"required", "min:" + strconv.Itoa(constants.PasswordMinLength), "max:" + strconv.Itoa(constants.PasswordMaxLength)}, DisplayName: "新密碼"},
	}

	validator := middleware.NewValidator(context.Background())
	for _, rule := range validationRules {
		validator.ValidateField(rule.Field, rule.Value, rule.Rules, rule.DisplayName)
	}

	result := validator.Result()
	if !result.Valid {
		return api.ErrBadRequest
	}
	return nil
}

// validateChangePasswordRequestWithContext 驗證密碼變更請求並回應錯誤
func (h *Handler) validateChangePasswordRequestWithContext(ctx context.Context, w http.ResponseWriter, req *ChangePasswordRequest) bool {
	validationRules := []middleware.ValidationRule{
		{Field: "old_password", Value: req.OldPassword, Rules: []string{"required"}, DisplayName: "現在密碼"},
		{Field: "new_password", Value: req.NewPassword, Rules: []string{"required", "min:" + strconv.Itoa(constants.PasswordMinLength), "max:" + strconv.Itoa(constants.PasswordMaxLength)}, DisplayName: "新密碼"},
	}

	return middleware.ValidateFieldsAndRespond(ctx, w, validationRules)
}

// validateUserStatus 驗證用戶狀態值
// 確保狀態值為系統允許的有效值
func (h *Handler) validateUserStatus(status sqlc.UserStatus) error {
	validStatuses := []string{
		string(constants.UserStatuses.Approved),
		string(constants.UserStatuses.PendingChange),
		string(constants.UserStatuses.ChangeRejected),
		string(constants.UserStatuses.Deleted),
	}

	validationRules := []middleware.ValidationRule{
		{Field: "status", Value: string(status), Rules: []string{"required", "oneof:" + strings.Join(validStatuses, " ")}, DisplayName: "用戶狀態"},
	}

	validator := middleware.NewValidator(context.Background())
	for _, rule := range validationRules {
		validator.ValidateField(rule.Field, rule.Value, rule.Rules, rule.DisplayName)
	}

	result := validator.Result()
	if !result.Valid {
		return api.ErrBadRequest
	}
	return nil
}

// validateUserStatusWithContext 驗證用戶狀態值並回應錯誤
func (h *Handler) validateUserStatusWithContext(ctx context.Context, w http.ResponseWriter, status sqlc.UserStatus) bool {
	validStatuses := []string{
		string(constants.UserStatuses.Approved),
		string(constants.UserStatuses.PendingChange),
		string(constants.UserStatuses.ChangeRejected),
		string(constants.UserStatuses.Deleted),
	}

	validationRules := []middleware.ValidationRule{
		{Field: "status", Value: string(status), Rules: []string{"required", "oneof:" + strings.Join(validStatuses, " ")}, DisplayName: "用戶狀態"},
	}

	return middleware.ValidateFieldsAndRespond(ctx, w, validationRules)
}

// 輔助方法

// handleServiceError 處理服務層錯誤 (deprecated - use handleServiceErrorWithContext)
func (h *Handler) handleServiceError(w http.ResponseWriter, err error) {
	h.handleServiceErrorWithContext(context.Background(), w, err)
}

// handleServiceErrorWithContext 處理服務層錯誤並包含請求上下文
// 使用新的資料庫錯誤處理系統提供更精確的錯誤訊息
func (h *Handler) handleServiceErrorWithContext(ctx context.Context, w http.ResponseWriter, err error) {
	// 先檢查是否為資料庫錯誤，提供更精確的錯誤訊息
	errMsg := api.DBErrorMessage(err, "使用者管理")
	if errMsg != "使用者管理失敗" {
		// 成功獲得具體錯誤訊息，直接使用
		api.InternalErrorWithContext(ctx, w, errMsg)
		return
	}

	// 回退到通用錯誤處理
	api.HandleCommonServiceErrorWithContext(ctx, w, err, h.logger, "user service")
}
